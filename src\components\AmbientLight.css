/* 氛围灯主容器 */
.ambient-light {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  z-index: 1000;
}

.ambient-light.transitioning {
  transition: all 0.15s ease;
}

/* 控制界面覆盖层 */
.controls-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 2rem;
  box-sizing: border-box;
}

.controls-overlay.visible {
  opacity: 1;
  visibility: visible;
}

/* 顶部控制栏 */
.top-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.back-icon {
  font-size: 1.2rem;
}

.mode-title {
  color: white;
  font-size: 2rem;
  font-weight: 600;
  text-align: center;
  flex: 1;
  margin: 0;
}

/* 亮度控制 */
.brightness-control {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.brightness-icon {
  font-size: 1.2rem;
}

.brightness-slider {
  width: 120px;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
}

.brightness-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  background: white;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.brightness-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: white;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.brightness-value {
  color: white;
  font-weight: 600;
  min-width: 40px;
  text-align: right;
}

/* 颜色预设区域 */
.color-presets {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.color-presets h3 {
  color: white;
  text-align: center;
  margin-bottom: 2rem;
  font-size: 1.5rem;
  font-weight: 500;
}

.preset-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
  max-width: 600px;
  margin: 0 auto;
}

.preset-item {
  aspect-ratio: 1;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid transparent;
  position: relative;
  overflow: hidden;
}

.preset-item:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.preset-item.active {
  border-color: white;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.preset-name {
  color: white;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  background: rgba(0, 0, 0, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 15px;
  backdrop-filter: blur(5px);
}

/* 底部提示 */
.bottom-hint {
  text-align: center;
}

.bottom-hint p {
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-size: 0.9rem;
}

/* 点击提示 */
.tap-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  animation: fadeInOut 3s ease-in-out infinite;
}

.tap-hint-content {
  background: rgba(0, 0, 0, 0.3);
  padding: 2rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.tap-icon {
  font-size: 3rem;
  display: block;
  margin-bottom: 1rem;
  animation: bounce 2s ease-in-out infinite;
}

.tap-hint p {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .controls-overlay {
    padding: 1rem;
  }
  
  .top-controls {
    flex-direction: column;
    gap: 1rem;
  }
  
  .mode-title {
    font-size: 1.5rem;
  }
  
  .brightness-control {
    order: 3;
  }
  
  .preset-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
  }
  
  .tap-hint-content {
    padding: 1.5rem;
  }
  
  .tap-icon {
    font-size: 2.5rem;
  }
}
