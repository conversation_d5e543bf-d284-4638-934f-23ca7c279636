/* 氛围灯主容器 */
.ambient-light {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  z-index: 1000;
  /* 移动端优化 */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* 防止移动端滚动 */
  touch-action: none;
  /* 确保全屏显示 */
  width: 100vw;
  height: 100vh;
  height: -webkit-fill-available; /* iOS Safari 兼容 */
}

.ambient-light.transitioning {
  transition: all 0.15s ease;
}

/* 动态效果 - 流动彩虹 */
.ambient-light.rainbow-flow {
  background: linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3);
  background-size: 400% 400%;
  animation: rainbow-flow-animation 8s ease-in-out infinite;
}

@keyframes rainbow-flow-animation {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* 动态效果 - 渐变呼吸 */
.ambient-light.color-breathing {
  background: radial-gradient(circle, #667eea 0%, #764ba2 50%, #667eea 100%);
  background-size: 200% 200%;
  animation: breathing-animation 4s ease-in-out infinite;
}

@keyframes breathing-animation {
  0%, 100% {
    background-size: 100% 100%;
    filter: brightness(0.8);
  }
  50% {
    background-size: 150% 150%;
    filter: brightness(1.2);
  }
}

/* 动态效果 - 爆闪节拍 */
.ambient-light.strobe-flash {
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 50%, #667eea 100%);
  animation: strobe-animation 0.8s ease-in-out infinite;
}

@keyframes strobe-animation {
  0%, 20%, 40%, 60%, 80%, 100% { opacity: 1; }
  10%, 30%, 50%, 70%, 90% { opacity: 0.3; }
}

/* 动态效果 - 波浪流动 */
.ambient-light.wave-flow {
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 25%, #4facfe 50%, #00f2fe 75%, #4facfe 100%);
  background-size: 300% 100%;
  animation: wave-flow-animation 6s linear infinite;
}

@keyframes wave-flow-animation {
  0% { background-position: 0% 0%; }
  100% { background-position: 300% 0%; }
}

/* 动态效果 - 极光舞动 */
.ambient-light.aurora-dance {
  background:
    radial-gradient(ellipse at top, #667eea 0%, transparent 50%),
    radial-gradient(ellipse at bottom, #764ba2 0%, transparent 50%),
    radial-gradient(ellipse at left, #4facfe 0%, transparent 50%),
    radial-gradient(ellipse at right, #00f2fe 0%, transparent 50%);
  animation: aurora-animation 10s ease-in-out infinite;
}

@keyframes aurora-animation {
  0%, 100% {
    filter: hue-rotate(0deg) brightness(1);
    transform: scale(1);
  }
  25% {
    filter: hue-rotate(90deg) brightness(1.2);
    transform: scale(1.05);
  }
  50% {
    filter: hue-rotate(180deg) brightness(0.9);
    transform: scale(0.95);
  }
  75% {
    filter: hue-rotate(270deg) brightness(1.1);
    transform: scale(1.02);
  }
}

/* 动态效果 - 霓虹脉冲 */
.ambient-light.neon-pulse {
  background: radial-gradient(circle, #ff0080 0%, #7928ca 50%, #ff0080 100%);
  animation: neon-pulse-animation 2s ease-in-out infinite;
}

@keyframes neon-pulse-animation {
  0%, 100% {
    box-shadow:
      inset 0 0 50px rgba(255, 0, 128, 0.5),
      0 0 100px rgba(255, 0, 128, 0.3);
    filter: brightness(1);
  }
  50% {
    box-shadow:
      inset 0 0 100px rgba(255, 0, 128, 0.8),
      0 0 200px rgba(255, 0, 128, 0.6);
    filter: brightness(1.3);
  }
}

/* 控制界面覆盖层 */
.controls-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 2rem;
  box-sizing: border-box;
  /* 移动端优化 */
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  /* 防止触摸穿透 */
  touch-action: manipulation;
}

.controls-overlay.visible {
  opacity: 1;
  visibility: visible;
}

/* 顶部控制栏 */
.top-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  backdrop-filter: blur(10px);
  /* 移动端优化 */
  min-height: 44px; /* iOS 推荐的最小触摸目标 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.back-icon {
  font-size: 1.2rem;
}

.mode-title {
  color: white;
  font-size: 2rem;
  font-weight: 600;
  text-align: center;
  flex: 1;
  margin: 0;
}

/* 亮度控制 */
.brightness-control {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.brightness-icon {
  font-size: 1.2rem;
}

.brightness-slider {
  width: 120px;
  height: 8px; /* 增加高度便于触摸 */
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  /* 移动端优化 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.brightness-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 24px; /* 增大触摸目标 */
  height: 24px;
  background: white;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  /* 移动端优化 */
  touch-action: manipulation;
}

.brightness-slider::-moz-range-thumb {
  width: 24px; /* 增大触摸目标 */
  height: 24px;
  background: white;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.brightness-value {
  color: white;
  font-weight: 600;
  min-width: 40px;
  text-align: right;
}

/* 模式选择 */
.mode-selection {
  margin-bottom: 2rem;
}

.mode-tabs {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  max-width: 500px;
  margin: 0 auto;
}

.mode-tab {
  flex: 1;
  padding: 0.8rem 1.5rem;
  border: none;
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  /* 移动端优化 */
  min-height: 44px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.mode-tab.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.mode-tab:hover:not(.active) {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

/* 颜色预设区域 */
.color-presets, .dynamic-modes, .custom-colors {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.color-presets h3, .dynamic-modes h3, .custom-colors h3 {
  color: white;
  text-align: center;
  margin-bottom: 2rem;
  font-size: 1.5rem;
  font-weight: 500;
}

.custom-colors h4 {
  color: white;
  text-align: center;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  font-weight: 500;
}

.preset-grid, .dynamic-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
  max-width: 600px;
  margin: 0 auto;
}

.preset-item {
  aspect-ratio: 1;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid transparent;
  position: relative;
  overflow: hidden;
  /* 移动端优化 */
  min-height: 80px; /* 确保足够的触摸目标 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.preset-item:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.preset-item.active {
  border-color: white;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.preset-name, .dynamic-name {
  color: white;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  background: rgba(0, 0, 0, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 15px;
  backdrop-filter: blur(5px);
}

/* 动态效果项目 */
.dynamic-item {
  aspect-ratio: 1;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 3px solid transparent;
  position: relative;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  /* 移动端优化 */
  min-height: 80px; /* 确保足够的触摸目标 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.dynamic-item:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.dynamic-item.active {
  border-color: white;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

/* 动态效果预览 */
.dynamic-preview {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-bottom: 1rem;
  position: relative;
}

.dynamic-preview.rainbow-flow {
  background: linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3);
  background-size: 200% 200%;
  animation: rainbow-flow-animation 2s ease-in-out infinite;
}

.dynamic-preview.color-breathing {
  background: radial-gradient(circle, #667eea 0%, #764ba2 100%);
  animation: breathing-preview 2s ease-in-out infinite;
}

@keyframes breathing-preview {
  0%, 100% { transform: scale(1); filter: brightness(0.8); }
  50% { transform: scale(1.1); filter: brightness(1.2); }
}

.dynamic-preview.strobe-flash {
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
  animation: strobe-preview 0.5s ease-in-out infinite;
}

@keyframes strobe-preview {
  0%, 50%, 100% { opacity: 1; }
  25%, 75% { opacity: 0.3; }
}

.dynamic-preview.wave-flow {
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 50%, #4facfe 100%);
  background-size: 200% 100%;
  animation: wave-preview 2s linear infinite;
}

@keyframes wave-preview {
  0% { background-position: 0% 0%; }
  100% { background-position: 200% 0%; }
}

.dynamic-preview.aurora-dance {
  background: radial-gradient(circle, #667eea 0%, #764ba2 100%);
  animation: aurora-preview 3s ease-in-out infinite;
}

@keyframes aurora-preview {
  0%, 100% { filter: hue-rotate(0deg); }
  33% { filter: hue-rotate(120deg); }
  66% { filter: hue-rotate(240deg); }
}

.dynamic-preview.neon-pulse {
  background: radial-gradient(circle, #ff0080 0%, #7928ca 100%);
  animation: neon-preview 1.5s ease-in-out infinite;
}

@keyframes neon-preview {
  0%, 100% {
    box-shadow: 0 0 10px rgba(255, 0, 128, 0.5);
    filter: brightness(1);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 0, 128, 0.8);
    filter: brightness(1.3);
  }
}

/* 底部提示 */
.bottom-hint {
  text-align: center;
}

.bottom-hint p {
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-size: 0.9rem;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .controls-overlay {
    padding: 1rem;
    /* 移动端安全区域适配 */
    padding-top: max(1rem, env(safe-area-inset-top));
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
  }

  .top-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .mode-title {
    font-size: 1.5rem;
  }

  .brightness-control {
    order: 3;
    width: 100%;
    justify-content: center;
  }

  .brightness-slider {
    width: 150px; /* 增加移动端滑块宽度 */
  }

  .preset-grid, .dynamic-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1rem;
  }

  .mode-tabs {
    max-width: 300px;
  }

  .mode-tab {
    padding: 0.8rem 1rem; /* 保持足够的触摸目标 */
    font-size: 0.9rem;
  }

  .dynamic-preview {
    width: 50px;
    height: 50px;
  }

  .back-button {
    align-self: flex-start;
    padding: 1rem 1.5rem; /* 增大移动端按钮 */
  }

  .preset-name, .dynamic-name {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}

/* 小屏幕设备优化 */
@media (max-width: 480px) {
  .controls-overlay {
    padding: 0.5rem;
    padding-top: max(0.5rem, env(safe-area-inset-top));
    padding-bottom: max(0.5rem, env(safe-area-inset-bottom));
  }

  .mode-title {
    font-size: 1.2rem;
  }

  .preset-grid, .dynamic-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.8rem;
  }

  .brightness-slider {
    width: 120px;
  }

  .mode-tabs {
    max-width: 250px;
  }

  .dynamic-preview {
    width: 40px;
    height: 40px;
  }
}

/* 自定义颜色样式 */
.color-picker-section {
  margin-bottom: 2rem;
}

.color-picker-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.color-picker {
  width: 60px;
  height: 60px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  background: none;
  outline: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  /* 移动端优化 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.color-picker:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.color-picker::-webkit-color-swatch-wrapper {
  padding: 0;
  border: none;
  border-radius: 50%;
}

.color-picker::-webkit-color-swatch {
  border: none;
  border-radius: 50%;
}

.color-picker::-moz-color-swatch {
  border: none;
  border-radius: 50%;
}

.color-picker-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: white;
  font-weight: 500;
}

.color-value {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  background: rgba(0, 0, 0, 0.3);
  padding: 0.3rem 0.8rem;
  border-radius: 10px;
  letter-spacing: 1px;
}

.save-color-btn {
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
  border: none;
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 15px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(74, 222, 128, 0.3);
  /* 移动端优化 */
  min-height: 44px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.save-color-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(74, 222, 128, 0.4);
}

.save-color-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 保存的颜色 */
.saved-colors-section {
  margin-bottom: 2rem;
}

.saved-colors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.saved-color-item {
  aspect-ratio: 1;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 3px solid transparent;
  position: relative;
  overflow: hidden;
  /* 移动端优化 */
  min-height: 80px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.saved-color-item:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.saved-color-item.active {
  border-color: white;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.saved-color-name {
  color: white;
  font-weight: 600;
  font-size: 0.8rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  background: rgba(0, 0, 0, 0.3);
  padding: 0.3rem 0.6rem;
  border-radius: 10px;
  backdrop-filter: blur(5px);
  margin-bottom: 0.5rem;
}

.delete-color-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  opacity: 0;
  /* 移动端优化 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.saved-color-item:hover .delete-color-btn {
  opacity: 1;
}

.delete-color-btn:hover {
  background: rgba(255, 0, 0, 1);
  transform: scale(1.1);
}

/* 快速颜色选择 */
.quick-colors-section {
  margin-bottom: 1rem;
}

.quick-colors-grid {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  gap: 0.5rem;
  max-width: 500px;
  margin: 0 auto;
}

.quick-color-item {
  aspect-ratio: 1;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  /* 移动端优化 */
  min-height: 30px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.quick-color-item:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.quick-color-item.active {
  border-color: white;
  box-shadow: 0 0 12px rgba(255, 255, 255, 0.6);
}

/* 移动端删除按钮优化 */
@media (max-width: 768px) {
  .delete-color-btn {
    opacity: 1; /* 移动端始终显示删除按钮 */
    width: 28px;
    height: 28px;
    font-size: 16px;
  }

  .color-picker-container {
    flex-direction: column;
    gap: 1.5rem;
    padding: 2rem 1rem;
  }

  .quick-colors-grid {
    grid-template-columns: repeat(8, 1fr);
  }

  .saved-colors-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .mode-tabs {
    max-width: 350px;
  }

  .mode-tab {
    padding: 0.6rem 0.8rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .quick-colors-grid {
    grid-template-columns: repeat(6, 1fr);
  }

  .saved-colors-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .color-picker {
    width: 50px;
    height: 50px;
  }

  .mode-tabs {
    max-width: 300px;
  }
}
