/* 走马灯主容器 */
.marquee-light {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  z-index: 1000;
  /* 移动端优化 */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* 防止移动端滚动 */
  touch-action: none;
  /* 确保全屏显示 */
  width: 100vw;
  height: 100vh;
  height: -webkit-fill-available; /* iOS Safari 兼容 */
}

.marquee-light.transitioning {
  transition: all 0.15s ease;
}

/* 走马灯容器 */
.marquee-container {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: auto;
  transform: translateY(-50%);
  overflow: hidden;
  white-space: nowrap;
}

/* 走马灯文字 */
.marquee-text {
  display: inline-block;
  white-space: nowrap;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  padding: 0 100vw;
  animation-fill-mode: both;
  animation-duration: 10s; /* 默认动画时长，会被内联样式覆盖 */
}

.marquee-text.left {
  animation-name: marquee-left;
}

.marquee-text.right {
  animation-name: marquee-right;
}

@keyframes marquee-left {
  0% {
    transform: translateX(100%);
  }
  70% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

@keyframes marquee-right {
  0% {
    transform: translateX(-100%);
  }
  70% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(100%);
  }
}



/* 控制界面覆盖层 */
.controls-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  padding: 0;
  box-sizing: border-box;
  /* 移动端优化 */
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  /* 防止触摸穿透 */
  touch-action: manipulation;
  /* 启用滚动 */
  overflow-y: auto;
  overflow-x: hidden;
}

.controls-overlay.visible {
  opacity: 1;
  visibility: visible;
}

/* 控制界面内容容器 */
.controls-content {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  padding: 2rem;
  gap: 2rem;
}

/* 顶部控制栏 */
.top-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
  flex-shrink: 0;
  position: sticky;
  top: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  padding: 1rem 0;
  margin: -1rem 0 1rem 0;
  z-index: 10;
}

.top-right-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  backdrop-filter: blur(10px);
  /* 移动端优化 */
  min-height: 44px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.back-icon {
  font-size: 1.2rem;
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  /* 移动端优化 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.close-button:hover {
  background: rgba(255, 0, 0, 0.3);
  border-color: rgba(255, 0, 0, 0.5);
  transform: scale(1.1);
}

.close-icon {
  font-size: 1.5rem;
  font-weight: bold;
  line-height: 1;
}

.mode-title {
  color: white;
  font-size: 2rem;
  font-weight: 600;
  text-align: center;
  flex: 1;
  margin: 0;
}

/* 亮度控制 */
.brightness-control {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.brightness-icon {
  font-size: 1.2rem;
}

.brightness-slider {
  width: 120px;
  height: 8px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  /* 移动端优化 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.brightness-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  touch-action: manipulation;
}

.brightness-slider::-moz-range-thumb {
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.brightness-value {
  color: white;
  font-weight: 600;
  min-width: 40px;
  text-align: right;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  min-height: 0;
}

/* 文字输入区域 */
.text-input-section {
  margin-bottom: 2rem;
}

.text-input-section h3 {
  color: white;
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 500;
}

.text-input-container {
  display: flex;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.text-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1rem;
  color: white;
  font-size: 1rem;
  resize: vertical;
  min-height: 80px;
  outline: none;
  /* 移动端优化 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.text-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.text-input:focus {
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.15);
}



/* 样式控制 */
.style-controls {
  background: rgba(255, 255, 255, 0.05);
  padding: 1.5rem;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.control-group {
  margin-bottom: 1.5rem;
}

.control-group:last-child {
  margin-bottom: 0;
}

.control-group label {
  display: block;
  color: white;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.control-slider {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  /* 移动端优化 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.control-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  touch-action: manipulation;
}

.control-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.direction-buttons {
  display: flex;
  gap: 1rem;
}

.direction-btn {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.8rem 1rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  /* 移动端优化 */
  min-height: 44px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.direction-btn:hover {
  background: rgba(255, 255, 255, 0.15);
}

.direction-btn.active {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
}

/* 颜色控制 */
.color-controls {
  background: rgba(255, 255, 255, 0.05);
  padding: 1.5rem;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.color-controls h4, .color-controls h5 {
  color: white;
  text-align: center;
  margin-bottom: 1rem;
  font-weight: 500;
}

.color-controls h4 {
  font-size: 1.2rem;
}

.color-controls h5 {
  font-size: 1rem;
  margin-top: 1.5rem;
}

.color-inputs {
  display: flex;
  gap: 2rem;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.color-input-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.color-input-group label {
  color: white;
  font-weight: 500;
  font-size: 0.9rem;
}

.color-picker {
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  background: none;
  outline: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  /* 移动端优化 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.color-picker:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.color-picker::-webkit-color-swatch-wrapper {
  padding: 0;
  border: none;
  border-radius: 50%;
}

.color-picker::-webkit-color-swatch {
  border: none;
  border-radius: 50%;
}

.color-picker::-moz-color-swatch {
  border: none;
  border-radius: 50%;
}

.color-value {
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  background: rgba(0, 0, 0, 0.3);
  padding: 0.2rem 0.5rem;
  border-radius: 8px;
  letter-spacing: 1px;
  color: white;
}

/* 颜色方案 */
.schemes-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.scheme-item {
  padding: 0.8rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  border: 2px solid transparent;
  /* 移动端优化 */
  min-height: 50px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.scheme-item:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.scheme-item.active {
  border-color: white;
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

.scheme-name {
  font-weight: 600;
  font-size: 0.85rem;
}

/* 分栏布局 */
.texts-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.texts-column {
  background: rgba(255, 255, 255, 0.05);
  padding: 1.5rem;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.texts-column h4 {
  color: white;
  text-align: center;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 500;
}

.preset-texts-grid, .history-texts-grid {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

/* 历史记录头部 */
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.clear-history-btn {
  background: rgba(255, 0, 0, 0.2);
  border: 1px solid rgba(255, 0, 0, 0.3);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  /* 移动端优化 */
  min-height: 36px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.clear-history-btn:hover {
  background: rgba(255, 0, 0, 0.3);
  border-color: rgba(255, 0, 0, 0.5);
}

.preset-text-item, .history-text-item {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid transparent;
  border-radius: 12px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  /* 移动端优化 */
  min-height: 60px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.preset-text-item:hover, .history-text-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.preset-text-item.active, .history-text-item.active {
  border-color: white;
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.preset-text-name {
  display: block;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 0.3rem;
}

.preset-text-preview, .history-text-preview {
  display: block;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.history-text-time {
  display: block;
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.7rem;
  margin-top: 0.3rem;
  font-style: italic;
}

.delete-text-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  opacity: 0;
  /* 移动端优化 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.history-text-item:hover .delete-text-btn {
  opacity: 1;
}

.delete-text-btn:hover {
  background: rgba(255, 0, 0, 1);
  transform: scale(1.1);
}

.empty-history {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  padding: 2rem 1rem;
}

.empty-history p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}



/* 滚动指示器 */
.scroll-indicator {
  position: fixed;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 100px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 20;
}

.controls-overlay.visible .scroll-indicator {
  opacity: 1;
}

.scroll-thumb {
  width: 100%;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 2px;
  transition: all 0.3s ease;
}

/* 滚动提示动画 */
@keyframes scrollHint {
  0%, 100% { transform: translateY(0); opacity: 0.6; }
  50% { transform: translateY(10px); opacity: 1; }
}

.scroll-hint {
  position: absolute;
  bottom: 2rem;
  right: 2rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
  animation: scrollHint 2s ease-in-out infinite;
  pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .controls-content {
    padding: 1rem;
    /* 移动端安全区域适配 */
    padding-top: max(1rem, env(safe-area-inset-top));
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
    gap: 1.5rem;
  }

  .top-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .top-right-controls {
    width: 100%;
    justify-content: space-between;
  }

  .mode-title {
    font-size: 1.5rem;
  }

  .brightness-control {
    flex: 1;
    justify-content: center;
  }

  .brightness-slider {
    width: 150px;
  }

  .texts-columns {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .color-inputs {
    flex-direction: column;
    gap: 1rem;
  }

  .schemes-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .text-input-container {
    flex-direction: column;
    gap: 1rem;
  }

  .direction-buttons {
    flex-direction: column;
    gap: 0.8rem;
  }

  .back-button {
    align-self: flex-start;
    padding: 1rem 1.5rem;
  }

  .delete-text-btn {
    opacity: 1; /* 移动端始终显示删除按钮 */
    width: 28px;
    height: 28px;
    font-size: 16px;
  }

  .scroll-hint {
    bottom: 1rem;
    right: 1rem;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .controls-content {
    padding: 0.5rem;
    padding-top: max(0.5rem, env(safe-area-inset-top));
    padding-bottom: max(0.5rem, env(safe-area-inset-bottom));
    gap: 1rem;
  }

  .top-controls {
    padding: 0.5rem 0;
    margin: -0.5rem 0 0.5rem 0;
  }

  .mode-title {
    font-size: 1.2rem;
  }

  .schemes-grid {
    grid-template-columns: 1fr;
    gap: 0.8rem;
  }

  .color-picker {
    width: 40px;
    height: 40px;
  }

  .brightness-slider {
    width: 120px;
  }

  .texts-column {
    padding: 1rem;
  }

  .style-controls, .color-controls {
    padding: 1rem;
  }

  .text-input {
    min-height: 60px;
  }
}
