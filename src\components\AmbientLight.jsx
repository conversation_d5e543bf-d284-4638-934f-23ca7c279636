import { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import './AmbientLight.css'

const AmbientLight = () => {
  const navigate = useNavigate()
  const [showControls, setShowControls] = useState(false)
  const [currentColor, setCurrentColor] = useState('#667eea')
  const [brightness, setBrightness] = useState(80)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const hideTimeoutRef = useRef(null)

  // 预设颜色方案 (3x3 = 9个)
  const presetColors = [
    { name: '深海蓝', color: '#667eea', gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', type: 'static' },
    { name: '温暖橙', color: '#ff9a56', gradient: 'linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%)', type: 'static' },
    { name: '森林绿', color: '#4ecdc4', gradient: 'linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%)', type: 'static' },
    { name: '薰衣草', color: '#a8edea', gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', type: 'static' },
    { name: '日落红', color: '#ff6b6b', gradient: 'linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%)', type: 'static' },
    { name: '夜空紫', color: '#667eea', gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', type: 'static' },
    { name: '清晨粉', color: '#ffeaa7', gradient: 'linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%)', type: 'static' },
    { name: '极光绿', color: '#00b894', gradient: 'linear-gradient(135deg, #00b894 0%, #00cec9 100%)', type: 'static' },
    { name: '星空银', color: '#74b9ff', gradient: 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)', type: 'static' }
  ]

  // 动态效果模式
  const dynamicModes = [
    { name: '流动彩虹', type: 'flowing', effect: 'rainbow-flow' },
    { name: '渐变呼吸', type: 'breathing', effect: 'color-breathing' },
    { name: '爆闪节拍', type: 'strobe', effect: 'strobe-flash' },
    { name: '波浪流动', type: 'wave', effect: 'wave-flow' },
    { name: '极光舞动', type: 'aurora', effect: 'aurora-dance' },
    { name: '霓虹脉冲', type: 'neon', effect: 'neon-pulse' }
  ]

  const [selectedPreset, setSelectedPreset] = useState(presetColors[0])
  const [selectedDynamic, setSelectedDynamic] = useState(null)
  const [currentMode, setCurrentMode] = useState('static') // 'static', 'dynamic', 'custom'
  const [customColor, setCustomColor] = useState('#667eea')
  const [customColor2, setCustomColor2] = useState('#764ba2')
  const [gradientDirection, setGradientDirection] = useState(135)
  const [savedColors, setSavedColors] = useState(() => {
    // 从 localStorage 加载保存的颜色
    const saved = localStorage.getItem('ambient-saved-colors')
    return saved ? JSON.parse(saved) : []
  })
  const [showColorPicker, setShowColorPicker] = useState(false)
  // 动态效果的自定义颜色
  const [dynamicColor1, setDynamicColor1] = useState('#667eea')
  const [dynamicColor2, setDynamicColor2] = useState('#764ba2')
  const [selectedDynamicCustom, setSelectedDynamicCustom] = useState(null)
  // 滚动相关状态
  const [showScrollHint, setShowScrollHint] = useState(false)
  const [scrollProgress, setScrollProgress] = useState(0)
  const controlsRef = useRef(null)

  // 点击屏幕显示/隐藏控制界面
  const handleScreenClick = () => {
    if (showControls) {
      // 如果控制界面已显示，点击任意位置隐藏
      setShowControls(false)
    } else {
      // 如果控制界面隐藏，点击显示
      setShowControls(true)
      resetHideTimeout()
    }
  }

  // 重置自动隐藏计时器
  const resetHideTimeout = () => {
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current)
    }

    // 设置5秒后自动隐藏
    hideTimeoutRef.current = setTimeout(() => {
      setShowControls(false)
    }, 5000)
  }

  // 监听控制界面显示状态，设置自动隐藏
  useEffect(() => {
    if (showControls) {
      resetHideTimeout()
      // 检查是否需要显示滚动提示
      setTimeout(() => {
        if (controlsRef.current) {
          const { scrollHeight, clientHeight } = controlsRef.current
          setShowScrollHint(scrollHeight > clientHeight)
        }
      }, 100)
    } else {
      setShowScrollHint(false)
    }

    return () => {
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current)
      }
    }
  }, [showControls])

  // 滚动事件处理
  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target
    const progress = scrollTop / (scrollHeight - clientHeight)
    setScrollProgress(Math.min(Math.max(progress, 0), 1))

    // 滚动时隐藏滚动提示
    if (showScrollHint && scrollTop > 50) {
      setShowScrollHint(false)
    }
  }

  // 防止移动端页面滚动和缩放
  useEffect(() => {
    // 禁用页面滚动
    document.body.style.overflow = 'hidden'
    document.body.style.position = 'fixed'
    document.body.style.width = '100%'
    document.body.style.height = '100%'

    // 禁用双击缩放
    const preventZoom = (e) => {
      if (e.touches.length > 1) {
        e.preventDefault()
      }
    }

    const preventDoubleClick = (e) => {
      e.preventDefault()
    }

    document.addEventListener('touchstart', preventZoom, { passive: false })
    document.addEventListener('touchmove', preventZoom, { passive: false })
    document.addEventListener('dblclick', preventDoubleClick)

    return () => {
      // 恢复页面滚动
      document.body.style.overflow = ''
      document.body.style.position = ''
      document.body.style.width = ''
      document.body.style.height = ''

      document.removeEventListener('touchstart', preventZoom)
      document.removeEventListener('touchmove', preventZoom)
      document.removeEventListener('dblclick', preventDoubleClick)
    }
  }, [])

  // 颜色切换动画
  const changeColor = (preset) => {
    setIsTransitioning(true)
    setTimeout(() => {
      setSelectedPreset(preset)
      setCurrentColor(preset.color)
      setCurrentMode('static')
      setSelectedDynamic(null)
      setIsTransitioning(false)
    }, 150)
    // 切换后直接隐藏控制界面
    setTimeout(() => {
      setShowControls(false)
    }, 300)
  }

  // 动态模式切换
  const changeDynamicMode = (mode) => {
    setIsTransitioning(true)
    setTimeout(() => {
      setSelectedDynamic(mode)
      setCurrentMode('dynamic')
      setIsTransitioning(false)
    }, 150)
    // 切换后直接隐藏控制界面
    setTimeout(() => {
      setShowControls(false)
    }, 300)
  }

  // 自定义颜色切换
  const changeCustomColor = (color1, color2 = null, direction = null) => {
    setIsTransitioning(true)
    setTimeout(() => {
      setCustomColor(color1)
      if (color2) setCustomColor2(color2)
      if (direction !== null) setGradientDirection(direction)
      setCurrentColor(color1)
      setCurrentMode('custom')
      setSelectedDynamic(null)
      setSelectedDynamicCustom(null)
      setIsTransitioning(false)
    }, 150)
    // 切换后直接隐藏控制界面
    setTimeout(() => {
      setShowControls(false)
    }, 300)
  }

  // 动态效果自定义颜色切换
  const changeDynamicCustomColor = (effect, color1, color2) => {
    setIsTransitioning(true)
    setTimeout(() => {
      setDynamicColor1(color1)
      setDynamicColor2(color2)
      setSelectedDynamicCustom(effect)
      setCurrentMode('dynamic')
      setSelectedDynamic(null)
      setIsTransitioning(false)
    }, 150)
    // 切换后直接隐藏控制界面
    setTimeout(() => {
      setShowControls(false)
    }, 300)
  }

  // 保存颜色到收藏
  const saveColor = () => {
    const colorName = `自定义 ${savedColors.length + 1}`
    const newColor = {
      id: Date.now(),
      name: colorName,
      color: customColor,
      color2: customColor2,
      direction: gradientDirection,
      gradient: `linear-gradient(${gradientDirection}deg, ${customColor} 0%, ${customColor2} 100%)`,
      timestamp: new Date().toISOString()
    }

    const updatedColors = [...savedColors, newColor]
    setSavedColors(updatedColors)
    localStorage.setItem('ambient-saved-colors', JSON.stringify(updatedColors))
  }

  // 删除保存的颜色
  const deleteSavedColor = (colorId) => {
    const updatedColors = savedColors.filter(color => color.id !== colorId)
    setSavedColors(updatedColors)
    localStorage.setItem('ambient-saved-colors', JSON.stringify(updatedColors))
  }

  // 调整颜色亮度的辅助函数
  const adjustBrightness = (hex, percent) => {
    const num = parseInt(hex.replace("#", ""), 16)
    const amt = Math.round(2.55 * percent)
    const R = (num >> 16) + amt
    const G = (num >> 8 & 0x00FF) + amt
    const B = (num & 0x0000FF) + amt
    return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
      (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
      (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1)
  }

  // 亮度调节
  const handleBrightnessChange = (value) => {
    setBrightness(value)
    resetHideTimeout()
  }

  // 返回主页
  const handleBack = () => {
    navigate('/')
  }

  // 阻止控制面板点击事件冒泡
  const handleControlClick = (e) => {
    e.stopPropagation()
    resetHideTimeout()
  }

  // 处理触摸事件
  const handleTouchStart = (e) => {
    e.preventDefault()
    handleScreenClick()
  }

  const handleControlTouch = (e) => {
    e.stopPropagation()
    resetHideTimeout()
  }

  // 获取当前背景样式
  const getCurrentBackground = () => {
    switch (currentMode) {
      case 'static':
        return selectedPreset.gradient
      case 'custom':
        return `linear-gradient(${gradientDirection}deg, ${customColor} 0%, ${customColor2} 100%)`
      case 'dynamic':
        return undefined // 动态效果通过 CSS 类控制
      default:
        return selectedPreset.gradient
    }
  }

  // 获取当前动态效果类名
  const getCurrentDynamicClass = () => {
    if (currentMode === 'dynamic') {
      if (selectedDynamicCustom) {
        return `custom-${selectedDynamicCustom}`
      } else if (selectedDynamic) {
        return selectedDynamic.effect
      }
    }
    return ''
  }

  return (
    <div
      className={`ambient-light ${isTransitioning ? 'transitioning' : ''} ${getCurrentDynamicClass()}`}
      style={{
        background: getCurrentBackground(),
        opacity: brightness / 100,
        '--dynamic-color1': dynamicColor1,
        '--dynamic-color2': dynamicColor2
      }}
      onClick={handleScreenClick}
      onTouchStart={handleTouchStart}
    >
      {/* 控制界面 */}
      <div
        ref={controlsRef}
        className={`controls-overlay ${showControls ? 'visible' : ''}`}
        onClick={handleControlClick}
        onTouchStart={handleControlTouch}
        onScroll={handleScroll}
      >
        <div className="controls-content">
          {/* 顶部控制栏 */}
          <div className="top-controls">
            <button className="back-button" onClick={handleBack}>
              <span className="back-icon">←</span>
              返回
            </button>
            <h2 className="mode-title">氛围灯</h2>
            <div className="brightness-control">
              <span className="brightness-icon">☀️</span>
              <input
                type="range"
                min="10"
                max="100"
                value={brightness}
                onChange={(e) => handleBrightnessChange(e.target.value)}
                className="brightness-slider"
              />
              <span className="brightness-value">{brightness}%</span>
            </div>
          </div>

          {/* 主内容区域 */}
          <div className="main-content">

        {/* 模式选择 */}
        <div className="mode-selection">
          <div className="mode-tabs">
            <button
              className={`mode-tab ${currentMode === 'static' ? 'active' : ''}`}
              onClick={() => setCurrentMode('static')}
            >
              预设颜色
            </button>
            <button
              className={`mode-tab ${currentMode === 'custom' ? 'active' : ''}`}
              onClick={() => setCurrentMode('custom')}
            >
              自定义
            </button>
            <button
              className={`mode-tab ${currentMode === 'dynamic' ? 'active' : ''}`}
              onClick={() => setCurrentMode('dynamic')}
            >
              动态效果
            </button>
          </div>
        </div>

        {/* 静态颜色预设 */}
        {currentMode === 'static' && (
          <div className="color-presets">
            <h3>颜色预设</h3>
            <div className="preset-grid">
              {presetColors.map((preset, index) => (
                <div
                  key={index}
                  className={`preset-item ${selectedPreset.name === preset.name ? 'active' : ''}`}
                  style={{ background: preset.gradient }}
                  onClick={() => changeColor(preset)}
                >
                  <span className="preset-name">{preset.name}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 自定义颜色 */}
        {currentMode === 'custom' && (
          <div className="custom-colors">
            <h3>自定义颜色</h3>

            {/* 渐变调色盘 */}
            <div className="gradient-picker-section">
              <div className="gradient-controls">
                <div className="color-inputs">
                  <div className="color-input-group">
                    <label>起始颜色</label>
                    <input
                      type="color"
                      value={customColor}
                      onChange={(e) => setCustomColor(e.target.value)}
                      className="color-picker"
                    />
                    <span className="color-value">{customColor.toUpperCase()}</span>
                  </div>
                  <div className="color-input-group">
                    <label>结束颜色</label>
                    <input
                      type="color"
                      value={customColor2}
                      onChange={(e) => setCustomColor2(e.target.value)}
                      className="color-picker"
                    />
                    <span className="color-value">{customColor2.toUpperCase()}</span>
                  </div>
                </div>

                <div className="gradient-direction">
                  <label>渐变方向: {gradientDirection}°</label>
                  <input
                    type="range"
                    min="0"
                    max="360"
                    value={gradientDirection}
                    onChange={(e) => setGradientDirection(e.target.value)}
                    className="direction-slider"
                  />
                </div>

                <div className="gradient-preview"
                     style={{ background: `linear-gradient(${gradientDirection}deg, ${customColor} 0%, ${customColor2} 100%)` }}>
                  预览
                </div>

                <div className="action-buttons">
                  <button
                    className="apply-color-btn"
                    onClick={() => changeCustomColor(customColor, customColor2, gradientDirection)}
                  >
                    ✨ 应用
                  </button>
                  <button
                    className="save-color-btn"
                    onClick={saveColor}
                    disabled={savedColors.length >= 12}
                  >
                    💾 保存
                  </button>
                </div>
              </div>
            </div>

            {/* 分栏布局：预设和收藏 */}
            <div className="colors-columns">
              {/* 预设颜色 */}
              <div className="colors-column">
                <h4>预设渐变</h4>
                <div className="preset-colors-grid">
                  {[
                    { name: '海洋蓝', gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color1: '#667eea', color2: '#764ba2' },
                    { name: '日落橙', gradient: 'linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%)', color1: '#ff9a56', color2: '#ff6b6b' },
                    { name: '森林绿', gradient: 'linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%)', color1: '#4ecdc4', color2: '#44a08d' },
                    { name: '薰衣草', gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', color1: '#a8edea', color2: '#fed6e3' },
                    { name: '火焰红', gradient: 'linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%)', color1: '#ff6b6b', color2: '#ffa726' },
                    { name: '夜空紫', gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color1: '#667eea', color2: '#764ba2' }
                  ].map((preset, index) => (
                    <div
                      key={index}
                      className={`preset-color-item ${customColor === preset.color1 && customColor2 === preset.color2 ? 'active' : ''}`}
                      style={{ background: preset.gradient }}
                      onClick={() => changeCustomColor(preset.color1, preset.color2, 135)}
                    >
                      <span className="preset-color-name">{preset.name}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* 收藏的颜色 */}
              <div className="colors-column">
                <h4>我的收藏 ({savedColors.length}/12)</h4>
                {savedColors.length > 0 ? (
                  <div className="saved-colors-grid">
                    {savedColors.map((color) => (
                      <div
                        key={color.id}
                        className={`saved-color-item ${customColor === color.color && customColor2 === color.color2 ? 'active' : ''}`}
                        style={{ background: color.gradient }}
                        onClick={() => changeCustomColor(color.color, color.color2, color.direction)}
                      >
                        <span className="saved-color-name">{color.name}</span>
                        <button
                          className="delete-color-btn"
                          onClick={(e) => {
                            e.stopPropagation()
                            deleteSavedColor(color.id)
                          }}
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="empty-saved">
                    <p>暂无收藏的颜色</p>
                    <p>创建渐变后点击保存</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 动态效果模式 */}
        {currentMode === 'dynamic' && (
          <div className="dynamic-modes">
            <h3>动态效果</h3>

            {/* 自定义动态效果调色盘 */}
            <div className="dynamic-color-picker">
              <h4>自定义动态颜色</h4>
              <div className="dynamic-color-controls">
                <div className="dynamic-color-inputs">
                  <div className="color-input-group">
                    <label>主色调</label>
                    <input
                      type="color"
                      value={dynamicColor1}
                      onChange={(e) => setDynamicColor1(e.target.value)}
                      className="color-picker small"
                    />
                    <span className="color-value small">{dynamicColor1.toUpperCase()}</span>
                  </div>
                  <div className="color-input-group">
                    <label>辅色调</label>
                    <input
                      type="color"
                      value={dynamicColor2}
                      onChange={(e) => setDynamicColor2(e.target.value)}
                      className="color-picker small"
                    />
                    <span className="color-value small">{dynamicColor2.toUpperCase()}</span>
                  </div>
                </div>

                <div className="dynamic-effects-grid">
                  {dynamicModes.map((mode, index) => (
                    <button
                      key={index}
                      className={`dynamic-effect-btn ${selectedDynamicCustom === mode.type ? 'active' : ''}`}
                      onClick={() => changeDynamicCustomColor(mode.type, dynamicColor1, dynamicColor2)}
                    >
                      <div className={`dynamic-preview-small custom-${mode.type}`}
                           style={{ '--dynamic-color1': dynamicColor1, '--dynamic-color2': dynamicColor2 }}></div>
                      <span>{mode.name}</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* 预设动态效果 */}
            <div className="preset-dynamic-section">
              <h4>预设动态效果</h4>
              <div className="dynamic-grid">
                {dynamicModes.map((mode, index) => (
                  <div
                    key={index}
                    className={`dynamic-item ${selectedDynamic?.name === mode.name ? 'active' : ''}`}
                    onClick={() => changeDynamicMode(mode)}
                  >
                    <div className={`dynamic-preview ${mode.effect}`}></div>
                    <span className="dynamic-name">{mode.name}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

          </div>

          {/* 底部区域 */}
          <div className="bottom-section">
            <div className="bottom-hint">
              <p>点击屏幕任意位置隐藏控制界面</p>
            </div>
          </div>
        </div>

        {/* 滚动指示器 */}
        <div className="scroll-indicator">
          <div
            className="scroll-thumb"
            style={{
              height: `${Math.max(scrollProgress * 100, 10)}%`,
              transform: `translateY(${scrollProgress * (100 - Math.max(scrollProgress * 100, 10))}px)`
            }}
          />
        </div>

        {/* 滚动提示 */}
        {showScrollHint && (
          <div className="scroll-hint">
            ↓ 向下滑动查看更多
          </div>
        )}
      </div>
    </div>
  )
}

export default AmbientLight
