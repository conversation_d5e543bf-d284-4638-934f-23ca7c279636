import { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import './AmbientLight.css'

const AmbientLight = () => {
  const navigate = useNavigate()
  const [showControls, setShowControls] = useState(false)
  const [currentColor, setCurrentColor] = useState('#667eea')
  const [brightness, setBrightness] = useState(80)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const hideTimeoutRef = useRef(null)

  // 预设颜色方案
  const presetColors = [
    { name: '深海蓝', color: '#667eea', gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', type: 'static' },
    { name: '温暖橙', color: '#ff9a56', gradient: 'linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%)', type: 'static' },
    { name: '森林绿', color: '#4ecdc4', gradient: 'linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%)', type: 'static' },
    { name: '薰衣草', color: '#a8edea', gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', type: 'static' },
    { name: '日落红', color: '#ff6b6b', gradient: 'linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%)', type: 'static' },
    { name: '夜空紫', color: '#667eea', gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', type: 'static' },
    { name: '清晨粉', color: '#ffeaa7', gradient: 'linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%)', type: 'static' },
    { name: '极光绿', color: '#00b894', gradient: 'linear-gradient(135deg, #00b894 0%, #00cec9 100%)', type: 'static' }
  ]

  // 动态效果模式
  const dynamicModes = [
    { name: '流动彩虹', type: 'flowing', effect: 'rainbow-flow' },
    { name: '渐变呼吸', type: 'breathing', effect: 'color-breathing' },
    { name: '爆闪节拍', type: 'strobe', effect: 'strobe-flash' },
    { name: '波浪流动', type: 'wave', effect: 'wave-flow' },
    { name: '极光舞动', type: 'aurora', effect: 'aurora-dance' },
    { name: '霓虹脉冲', type: 'neon', effect: 'neon-pulse' }
  ]

  const [selectedPreset, setSelectedPreset] = useState(presetColors[0])
  const [selectedDynamic, setSelectedDynamic] = useState(null)
  const [currentMode, setCurrentMode] = useState('static') // 'static' 或 'dynamic'

  // 点击屏幕显示/隐藏控制界面
  const handleScreenClick = () => {
    setShowControls(!showControls)
    resetHideTimeout()
  }

  // 重置自动隐藏计时器
  const resetHideTimeout = () => {
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current)
    }

    if (showControls) {
      hideTimeoutRef.current = setTimeout(() => {
        setShowControls(false)
      }, 5000) // 5秒后自动隐藏
    }
  }

  // 监听控制界面显示状态，设置自动隐藏
  useEffect(() => {
    if (showControls) {
      resetHideTimeout()
    }

    return () => {
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current)
      }
    }
  }, [showControls])

  // 颜色切换动画
  const changeColor = (preset) => {
    setIsTransitioning(true)
    setTimeout(() => {
      setSelectedPreset(preset)
      setCurrentColor(preset.color)
      setCurrentMode('static')
      setSelectedDynamic(null)
      setIsTransitioning(false)
    }, 150)
    // 切换后直接隐藏控制界面
    setTimeout(() => {
      setShowControls(false)
    }, 300)
  }

  // 动态模式切换
  const changeDynamicMode = (mode) => {
    setIsTransitioning(true)
    setTimeout(() => {
      setSelectedDynamic(mode)
      setCurrentMode('dynamic')
      setIsTransitioning(false)
    }, 150)
    // 切换后直接隐藏控制界面
    setTimeout(() => {
      setShowControls(false)
    }, 300)
  }

  // 亮度调节
  const handleBrightnessChange = (value) => {
    setBrightness(value)
    resetHideTimeout()
  }

  // 返回主页
  const handleBack = () => {
    navigate('/')
  }

  // 阻止控制面板点击事件冒泡
  const handleControlClick = (e) => {
    e.stopPropagation()
    resetHideTimeout()
  }

  return (
    <div
      className={`ambient-light ${isTransitioning ? 'transitioning' : ''} ${currentMode === 'dynamic' && selectedDynamic ? selectedDynamic.effect : ''}`}
      style={{
        background: currentMode === 'static' ? selectedPreset.gradient : undefined,
        opacity: brightness / 100
      }}
      onClick={handleScreenClick}
    >
      {/* 控制界面 */}
      <div
        className={`controls-overlay ${showControls ? 'visible' : ''}`}
        onClick={handleControlClick}
      >
        {/* 顶部控制栏 */}
        <div className="top-controls">
          <button className="back-button" onClick={handleBack}>
            <span className="back-icon">←</span>
            返回
          </button>
          <h2 className="mode-title">氛围灯</h2>
          <div className="brightness-control">
            <span className="brightness-icon">☀️</span>
            <input
              type="range"
              min="10"
              max="100"
              value={brightness}
              onChange={(e) => handleBrightnessChange(e.target.value)}
              className="brightness-slider"
            />
            <span className="brightness-value">{brightness}%</span>
          </div>
        </div>

        {/* 模式选择 */}
        <div className="mode-selection">
          <div className="mode-tabs">
            <button
              className={`mode-tab ${currentMode === 'static' ? 'active' : ''}`}
              onClick={() => setCurrentMode('static')}
            >
              静态颜色
            </button>
            <button
              className={`mode-tab ${currentMode === 'dynamic' ? 'active' : ''}`}
              onClick={() => setCurrentMode('dynamic')}
            >
              动态效果
            </button>
          </div>
        </div>

        {/* 静态颜色预设 */}
        {currentMode === 'static' && (
          <div className="color-presets">
            <h3>颜色预设</h3>
            <div className="preset-grid">
              {presetColors.map((preset, index) => (
                <div
                  key={index}
                  className={`preset-item ${selectedPreset.name === preset.name ? 'active' : ''}`}
                  style={{ background: preset.gradient }}
                  onClick={() => changeColor(preset)}
                >
                  <span className="preset-name">{preset.name}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 动态效果模式 */}
        {currentMode === 'dynamic' && (
          <div className="dynamic-modes">
            <h3>动态效果</h3>
            <div className="dynamic-grid">
              {dynamicModes.map((mode, index) => (
                <div
                  key={index}
                  className={`dynamic-item ${selectedDynamic?.name === mode.name ? 'active' : ''}`}
                  onClick={() => changeDynamicMode(mode)}
                >
                  <div className={`dynamic-preview ${mode.effect}`}></div>
                  <span className="dynamic-name">{mode.name}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 底部提示 */}
        <div className="bottom-hint">
          <p>点击屏幕任意位置隐藏控制界面</p>
        </div>
      </div>


    </div>
  )
}

export default AmbientLight
