import { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import './MarqueeLight.css'

const MarqueeLight = () => {
  const navigate = useNavigate()
  const [showControls, setShowControls] = useState(false)
  const [backgroundColor, setBackgroundColor] = useState('#000000')
  const [textColor, setTextColor] = useState('#ffffff')
  const [text, setText] = useState('欢迎使用走马灯功能 ✨ 这里可以显示任何文字内容')
  const [fontSize, setFontSize] = useState(48)
  const [speed, setSpeed] = useState(50)
  const [direction, setDirection] = useState('left')
  const [brightness, setBrightness] = useState(80)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const hideTimeoutRef = useRef(null)
  const controlsRef = useRef(null)
  const [showScrollHint, setShowScrollHint] = useState(false)
  const [scrollProgress, setScrollProgress] = useState(0)
  const [savedTexts, setSavedTexts] = useState(() => {
    // 从 localStorage 加载保存的文字
    const saved = localStorage.getItem('marquee-saved-texts')
    return saved ? JSON.parse(saved) : []
  })

  // 预设文字内容
  const presetTexts = [
    { name: '欢迎信息', text: '欢迎光临！Welcome！' },
    { name: '节日祝福', text: '🎉 新年快乐！恭喜发财！🎊' },
    { name: '生日快乐', text: '🎂 生日快乐！Happy Birthday！🎈' },
    { name: '营业时间', text: '营业时间：09:00-21:00 欢迎光临' },
    { name: '促销信息', text: '🔥 限时优惠！全场8折！机会难得！' },
    { name: '温馨提示', text: '请保持安静 · 禁止吸烟 · 谢谢合作' },
    { name: '联系方式', text: '客服热线：400-123-4567 随时为您服务' },
    { name: '安全提醒', text: '⚠️ 注意安全 · 小心台阶 · 请勿奔跑' },
    { name: '感谢光临', text: '感谢您的光临！期待下次再见！👋' }
  ]

  // 预设颜色方案
  const colorSchemes = [
    { name: '经典黑白', bg: '#000000', text: '#ffffff' },
    { name: '红色警示', bg: '#dc2626', text: '#ffffff' },
    { name: '蓝色科技', bg: '#1e40af', text: '#ffffff' },
    { name: '绿色环保', bg: '#059669', text: '#ffffff' },
    { name: '橙色活力', bg: '#ea580c', text: '#ffffff' },
    { name: '紫色神秘', bg: '#7c3aed', text: '#ffffff' },
    { name: '黄色警告', bg: '#ca8a04', text: '#000000' },
    { name: '粉色温馨', bg: '#ec4899', text: '#ffffff' },
    { name: '青色清新', bg: '#0891b2', text: '#ffffff' }
  ]

  // 点击屏幕显示/隐藏控制界面
  const handleScreenClick = () => {
    if (showControls) {
      setShowControls(false)
    } else {
      setShowControls(true)
      resetHideTimeout()
    }
  }

  // 重置自动隐藏计时器
  const resetHideTimeout = () => {
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current)
    }

    hideTimeoutRef.current = setTimeout(() => {
      setShowControls(false)
    }, 8000) // 8秒后自动隐藏，比其他功能稍长一些
  }

  // 监听控制界面显示状态
  useEffect(() => {
    if (showControls) {
      resetHideTimeout()
      setTimeout(() => {
        if (controlsRef.current) {
          const { scrollHeight, clientHeight } = controlsRef.current
          setShowScrollHint(scrollHeight > clientHeight)
        }
      }, 100)
    } else {
      setShowScrollHint(false)
    }

    return () => {
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current)
      }
    }
  }, [showControls])

  // 滚动事件处理
  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target
    const progress = scrollTop / (scrollHeight - clientHeight)
    setScrollProgress(Math.min(Math.max(progress, 0), 1))

    if (showScrollHint && scrollTop > 50) {
      setShowScrollHint(false)
    }
  }

  // 防止移动端页面滚动和缩放
  useEffect(() => {
    document.body.style.overflow = 'hidden'
    document.body.style.position = 'fixed'
    document.body.style.width = '100%'
    document.body.style.height = '100%'

    const preventZoom = (e) => {
      if (e.touches.length > 1) {
        e.preventDefault()
      }
    }

    const preventDoubleClick = (e) => {
      e.preventDefault()
    }

    document.addEventListener('touchstart', preventZoom, { passive: false })
    document.addEventListener('touchmove', preventZoom, { passive: false })
    document.addEventListener('dblclick', preventDoubleClick)

    return () => {
      document.body.style.overflow = ''
      document.body.style.position = ''
      document.body.style.width = ''
      document.body.style.height = ''

      document.removeEventListener('touchstart', preventZoom)
      document.removeEventListener('touchmove', preventZoom)
      document.removeEventListener('dblclick', preventDoubleClick)
    }
  }, [])

  // 应用预设文字
  const applyPresetText = (preset) => {
    setIsTransitioning(true)
    setTimeout(() => {
      setText(preset.text)
      setIsTransitioning(false)
    }, 150)
    setTimeout(() => {
      setShowControls(false)
    }, 300)
  }

  // 应用颜色方案
  const applyColorScheme = (scheme) => {
    setIsTransitioning(true)
    setTimeout(() => {
      setBackgroundColor(scheme.bg)
      setTextColor(scheme.text)
      setIsTransitioning(false)
    }, 150)
    resetHideTimeout()
  }

  // 保存文字
  const saveText = () => {
    if (!text.trim()) return

    const textName = `文字 ${savedTexts.length + 1}`
    const newText = {
      id: Date.now(),
      name: textName,
      text: text.trim(),
      timestamp: new Date().toISOString()
    }

    const updatedTexts = [...savedTexts, newText]
    setSavedTexts(updatedTexts)
    localStorage.setItem('marquee-saved-texts', JSON.stringify(updatedTexts))
  }

  // 删除保存的文字
  const deleteSavedText = (textId) => {
    const updatedTexts = savedTexts.filter(item => item.id !== textId)
    setSavedTexts(updatedTexts)
    localStorage.setItem('marquee-saved-texts', JSON.stringify(updatedTexts))
  }

  // 返回主页
  const handleBack = () => {
    navigate('/')
  }

  // 阻止控制面板点击事件冒泡
  const handleControlClick = (e) => {
    e.stopPropagation()
    resetHideTimeout()
  }

  const handleTouchStart = (e) => {
    e.preventDefault()
    handleScreenClick()
  }

  const handleControlTouch = (e) => {
    e.stopPropagation()
    resetHideTimeout()
  }

  return (
    <div
      className={`marquee-light ${isTransitioning ? 'transitioning' : ''}`}
      style={{
        backgroundColor: backgroundColor,
        opacity: brightness / 100
      }}
      onClick={handleScreenClick}
      onTouchStart={handleTouchStart}
    >
      {/* 走马灯文字 */}
      <div className="marquee-container">
        <div
          className={`marquee-text ${direction}`}
          style={{
            color: textColor,
            fontSize: `${fontSize}px`,
            animationDuration: `${101 - speed}s`
          }}
        >
          {text}
        </div>
      </div>

      {/* 控制界面 */}
      <div
        ref={controlsRef}
        className={`controls-overlay ${showControls ? 'visible' : ''}`}
        onClick={handleControlClick}
        onTouchStart={handleControlTouch}
        onScroll={handleScroll}
      >
        <div className="controls-content">
          {/* 顶部控制栏 */}
          <div className="top-controls">
            <button className="back-button" onClick={handleBack}>
              <span className="back-icon">←</span>
              返回
            </button>
            <h2 className="mode-title">走马灯</h2>
            <div className="top-right-controls">
              <div className="brightness-control">
                <span className="brightness-icon">☀️</span>
                <input
                  type="range"
                  min="10"
                  max="100"
                  value={brightness}
                  onChange={(e) => setBrightness(e.target.value)}
                  className="brightness-slider"
                />
                <span className="brightness-value">{brightness}%</span>
              </div>
              <button className="close-button" onClick={() => setShowControls(false)}>
                <span className="close-icon">×</span>
              </button>
            </div>
          </div>

          {/* 主内容区域 */}
          <div className="main-content">
            {/* 文字输入区域 */}
            <div className="text-input-section">
              <h3>文字内容</h3>
              <div className="text-input-container">
                <textarea
                  value={text}
                  onChange={(e) => setText(e.target.value)}
                  placeholder="请输入要显示的文字内容..."
                  className="text-input"
                  rows="3"
                />
                <button
                  className="save-text-btn"
                  onClick={saveText}
                  disabled={!text.trim() || savedTexts.length >= 12}
                >
                  💾 保存
                </button>
              </div>
            </div>

            {/* 样式控制 */}
            <div className="style-controls">
              <div className="control-group">
                <label>字体大小: {fontSize}px</label>
                <input
                  type="range"
                  min="24"
                  max="120"
                  value={fontSize}
                  onChange={(e) => setFontSize(e.target.value)}
                  className="control-slider"
                />
              </div>

              <div className="control-group">
                <label>滚动速度: {speed}%</label>
                <input
                  type="range"
                  min="10"
                  max="100"
                  value={speed}
                  onChange={(e) => setSpeed(e.target.value)}
                  className="control-slider"
                />
              </div>

              <div className="control-group">
                <label>滚动方向</label>
                <div className="direction-buttons">
                  <button
                    className={`direction-btn ${direction === 'left' ? 'active' : ''}`}
                    onClick={() => setDirection('left')}
                  >
                    ← 向左
                  </button>
                  <button
                    className={`direction-btn ${direction === 'right' ? 'active' : ''}`}
                    onClick={() => setDirection('right')}
                  >
                    向右 →
                  </button>
                </div>
              </div>
            </div>

            {/* 颜色控制 */}
            <div className="color-controls">
              <h4>颜色设置</h4>
              <div className="color-inputs">
                <div className="color-input-group">
                  <label>背景颜色</label>
                  <input
                    type="color"
                    value={backgroundColor}
                    onChange={(e) => setBackgroundColor(e.target.value)}
                    className="color-picker"
                  />
                  <span className="color-value">{backgroundColor.toUpperCase()}</span>
                </div>
                <div className="color-input-group">
                  <label>文字颜色</label>
                  <input
                    type="color"
                    value={textColor}
                    onChange={(e) => setTextColor(e.target.value)}
                    className="color-picker"
                  />
                  <span className="color-value">{textColor.toUpperCase()}</span>
                </div>
              </div>

              {/* 颜色方案 */}
              <div className="color-schemes">
                <h5>快速配色</h5>
                <div className="schemes-grid">
                  {colorSchemes.map((scheme, index) => (
                    <div
                      key={index}
                      className={`scheme-item ${backgroundColor === scheme.bg && textColor === scheme.text ? 'active' : ''}`}
                      style={{ backgroundColor: scheme.bg, color: scheme.text }}
                      onClick={() => applyColorScheme(scheme)}
                    >
                      <span className="scheme-name">{scheme.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* 分栏布局：预设和收藏 */}
            <div className="texts-columns">
              {/* 预设文字 */}
              <div className="texts-column">
                <h4>预设文字</h4>
                <div className="preset-texts-grid">
                  {presetTexts.map((preset, index) => (
                    <div
                      key={index}
                      className={`preset-text-item ${text === preset.text ? 'active' : ''}`}
                      onClick={() => applyPresetText(preset)}
                    >
                      <span className="preset-text-name">{preset.name}</span>
                      <span className="preset-text-preview">{preset.text}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* 收藏的文字 */}
              <div className="texts-column">
                <h4>我的收藏 ({savedTexts.length}/12)</h4>
                {savedTexts.length > 0 ? (
                  <div className="saved-texts-grid">
                    {savedTexts.map((item) => (
                      <div
                        key={item.id}
                        className={`saved-text-item ${text === item.text ? 'active' : ''}`}
                        onClick={() => setText(item.text)}
                      >
                        <span className="saved-text-name">{item.name}</span>
                        <span className="saved-text-preview">{item.text}</span>
                        <button
                          className="delete-text-btn"
                          onClick={(e) => {
                            e.stopPropagation()
                            deleteSavedText(item.id)
                          }}
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="empty-saved">
                    <p>暂无收藏的文字</p>
                    <p>输入文字后点击保存</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 底部区域 */}
          <div className="bottom-section">
            <div className="bottom-hint">
              <p>点击屏幕任意位置隐藏控制界面</p>
            </div>
          </div>
        </div>

        {/* 滚动指示器 */}
        <div className="scroll-indicator">
          <div
            className="scroll-thumb"
            style={{
              height: `${Math.max(scrollProgress * 100, 10)}%`,
              transform: `translateY(${scrollProgress * (100 - Math.max(scrollProgress * 100, 10))}px)`
            }}
          />
        </div>

        {/* 滚动提示 */}
        {showScrollHint && (
          <div className="scroll-hint">
            ↓ 向下滑动查看更多
          </div>
        )}
      </div>
    </div>
  )
}

export default MarqueeLight
