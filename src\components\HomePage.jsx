import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import '../App.css'

function HomePage() {
  const navigate = useNavigate()
  const [selectedMode, setSelectedMode] = useState(null)

  const lightModes = [
    {
      id: 'ambient',
      title: '氛围灯',
      description: '营造温馨舒适的环境氛围',
      icon: '🌙',
      color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      features: ['多种颜色模式', '亮度调节', '定时开关'],
      path: '/ambient'
    },
    {
      id: 'solid',
      title: '纯色灯',
      description: '单一颜色的稳定照明',
      icon: '💡',
      color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      features: ['RGB调色', '亮度控制', '颜色预设'],
      path: '/solid'
    },
    {
      id: 'marquee',
      title: '走马灯',
      description: '动态流光效果展示',
      icon: '✨',
      color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      features: ['流光效果', '速度调节', '方向控制'],
      path: '/marquee'
    }
  ]

  const handleModeClick = (mode) => {
    if (mode.id === 'ambient' || mode.id === 'solid') {
      // 氛围灯和纯色灯功能已实现，直接跳转
      navigate(mode.path)
    } else {
      // 其他功能暂未实现，显示选中状态
      setSelectedMode(selectedMode === mode.id ? null : mode.id)
    }
  }

  return (
    <div className="app">
      <header className="header">
        <div className="header-content">
          <h1 className="title">
            <span className="title-icon">💫</span>
            光效控制系统
          </h1>
          <p className="subtitle">智能灯光控制，打造完美光影体验</p>
        </div>
      </header>

      <main className="main">
        <div className="modes-grid">
          {lightModes.map((mode) => (
            <div
              key={mode.id}
              className={`mode-card ${selectedMode === mode.id ? 'selected' : ''} ${(mode.id === 'ambient' || mode.id === 'solid') ? 'available' : ''}`}
              onClick={() => handleModeClick(mode)}
              style={{ '--card-gradient': mode.color }}
            >
              <div className="mode-header">
                <span className="mode-icon">{mode.icon}</span>
                <h3 className="mode-title">{mode.title}</h3>
                {(mode.id === 'ambient' || mode.id === 'solid') && (
                  <span className="available-badge">可用</span>
                )}
              </div>

              <p className="mode-description">{mode.description}</p>

              <div className="mode-features">
                {mode.features.map((feature, index) => (
                  <span key={index} className="feature-tag">
                    {feature}
                  </span>
                ))}
              </div>

              <div className="mode-status">
                <span className="status-indicator"></span>
                <span className="status-text">
                  {(mode.id === 'ambient' || mode.id === 'solid')
                    ? '点击进入'
                    : selectedMode === mode.id
                      ? '已选中'
                      : '点击选择'
                  }
                </span>
              </div>
            </div>
          ))}
        </div>

        {selectedMode && selectedMode !== 'ambient' && selectedMode !== 'solid' && (
          <div className="control-panel">
            <h4>控制面板</h4>
            <p>功能开发中，敬请期待...</p>
            <div className="coming-soon">
              <span>🚧</span>
              <span>功能即将上线</span>
            </div>
          </div>
        )}
      </main>

      <footer className="footer">
        <p>&copy; 2024 光效控制系统 - 让光影更智能</p>
      </footer>
    </div>
  )
}

export default HomePage
