/* 纯色灯主容器 */
.solid-light {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  z-index: 1000;
  /* 移动端优化 */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* 防止移动端滚动 */
  touch-action: none;
  /* 确保全屏显示 */
  width: 100vw;
  height: 100vh;
  height: -webkit-fill-available; /* iOS Safari 兼容 */
}

.solid-light.transitioning {
  transition: all 0.15s ease;
}

/* 控制界面覆盖层 */
.controls-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  padding: 0;
  box-sizing: border-box;
  /* 移动端优化 */
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  /* 防止触摸穿透 */
  touch-action: manipulation;
  /* 启用滚动 */
  overflow-y: auto;
  overflow-x: hidden;
}

.controls-overlay.visible {
  opacity: 1;
  visibility: visible;
}

/* 控制界面内容容器 */
.controls-content {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  padding: 2rem;
  gap: 2rem;
}

/* 顶部控制栏 */
.top-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
  flex-shrink: 0;
  position: sticky;
  top: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  padding: 1rem 0;
  margin: -1rem 0 1rem 0;
  z-index: 10;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  backdrop-filter: blur(10px);
  /* 移动端优化 */
  min-height: 44px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.back-icon {
  font-size: 1.2rem;
}

.mode-title {
  color: white;
  font-size: 2rem;
  font-weight: 600;
  text-align: center;
  flex: 1;
  margin: 0;
}

/* 亮度控制 */
.brightness-control {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.brightness-icon {
  font-size: 1.2rem;
}

.brightness-slider {
  width: 120px;
  height: 8px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  /* 移动端优化 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.brightness-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  touch-action: manipulation;
}

.brightness-slider::-moz-range-thumb {
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.brightness-value {
  color: white;
  font-weight: 600;
  min-width: 40px;
  text-align: right;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  min-height: 0;
}

/* 调色盘区域 */
.color-picker-section {
  margin-bottom: 2rem;
}

.color-picker-section h3 {
  color: white;
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 500;
}

.color-picker-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.color-picker {
  width: 60px;
  height: 60px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  background: none;
  outline: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  /* 移动端优化 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.color-picker:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.color-picker::-webkit-color-swatch-wrapper {
  padding: 0;
  border: none;
  border-radius: 50%;
}

.color-picker::-webkit-color-swatch {
  border: none;
  border-radius: 50%;
}

.color-picker::-moz-color-swatch {
  border: none;
  border-radius: 50%;
}

.color-picker-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: white;
  font-weight: 500;
}

.color-value {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  background: rgba(0, 0, 0, 0.3);
  padding: 0.3rem 0.8rem;
  border-radius: 10px;
  letter-spacing: 1px;
}

.save-color-btn {
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
  border: none;
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 15px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(74, 222, 128, 0.3);
  /* 移动端优化 */
  min-height: 44px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.save-color-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(74, 222, 128, 0.4);
}

.save-color-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 分栏布局 */
.colors-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.colors-column {
  background: rgba(255, 255, 255, 0.05);
  padding: 1.5rem;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.colors-column h4 {
  color: white;
  text-align: center;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 500;
}

.preset-colors-grid, .saved-colors-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.preset-color-item, .saved-color-item {
  aspect-ratio: 1;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  min-height: 60px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.preset-color-item:hover, .saved-color-item:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.preset-color-item.active, .saved-color-item.active {
  border-color: white;
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

.preset-color-name, .saved-color-name {
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
  background: rgba(0, 0, 0, 0.5);
  padding: 0.25rem 0.5rem;
  border-radius: 8px;
  backdrop-filter: blur(5px);
}

.delete-color-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  opacity: 0;
  /* 移动端优化 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.saved-color-item:hover .delete-color-btn {
  opacity: 1;
}

.delete-color-btn:hover {
  background: rgba(255, 0, 0, 1);
  transform: scale(1.1);
}

.empty-saved {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  padding: 2rem 1rem;
}

.empty-saved p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

/* 底部区域 */
.bottom-section {
  flex-shrink: 0;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: auto;
}

/* 底部提示 */
.bottom-hint {
  text-align: center;
}

.bottom-hint p {
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-size: 0.9rem;
}

/* 滚动指示器 */
.scroll-indicator {
  position: fixed;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 100px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 20;
}

.controls-overlay.visible .scroll-indicator {
  opacity: 1;
}

.scroll-thumb {
  width: 100%;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 2px;
  transition: all 0.3s ease;
}

/* 滚动提示动画 */
@keyframes scrollHint {
  0%, 100% { transform: translateY(0); opacity: 0.6; }
  50% { transform: translateY(10px); opacity: 1; }
}

.scroll-hint {
  position: absolute;
  bottom: 2rem;
  right: 2rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
  animation: scrollHint 2s ease-in-out infinite;
  pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .controls-content {
    padding: 1rem;
    /* 移动端安全区域适配 */
    padding-top: max(1rem, env(safe-area-inset-top));
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
    gap: 1.5rem;
  }

  .top-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .mode-title {
    font-size: 1.5rem;
  }

  .brightness-control {
    order: 3;
    width: 100%;
    justify-content: center;
  }

  .brightness-slider {
    width: 150px;
  }

  .colors-columns {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .preset-colors-grid, .saved-colors-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .color-picker-container {
    flex-direction: column;
    gap: 1.5rem;
    padding: 2rem 1rem;
  }

  .back-button {
    align-self: flex-start;
    padding: 1rem 1.5rem;
  }

  .preset-color-name, .saved-color-name {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }

  .delete-color-btn {
    opacity: 1; /* 移动端始终显示删除按钮 */
    width: 28px;
    height: 28px;
    font-size: 16px;
  }

  .scroll-hint {
    bottom: 1rem;
    right: 1rem;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .controls-content {
    padding: 0.5rem;
    padding-top: max(0.5rem, env(safe-area-inset-top));
    padding-bottom: max(0.5rem, env(safe-area-inset-bottom));
    gap: 1rem;
  }

  .top-controls {
    padding: 0.5rem 0;
    margin: -0.5rem 0 0.5rem 0;
  }

  .mode-title {
    font-size: 1.2rem;
  }

  .preset-colors-grid, .saved-colors-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
  }

  .color-picker {
    width: 50px;
    height: 50px;
  }

  .brightness-slider {
    width: 120px;
  }

  .colors-column {
    padding: 1rem;
  }
}
